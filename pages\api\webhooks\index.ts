// pages/api/webhooks/index.ts
import type { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).send('Method Not Allowed');
  }

  try {
    const cartId = req.body?.data?.id;
    console.log('🛒 Cart Deleted Webhook Triggered');
    console.log('🧾 Deleted Cart ID:', cartId);

    res.status(200).json({ received: true });
  } catch (error) {
    console.error('Error handling webhook:', error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
}
